# Genre Preservation Testing Guide

## Table of Contents

- [Overview](#overview)
- [Genre Preservation Strategy Testing](#genre-preservation-strategy-testing)
- [Backward Compatibility Testing](#backward-compatibility-testing)
- [Migration Testing](#migration-testing)
- [Taxonomy Integration Testing](#taxonomy-integration-testing)
- [Performance Testing](#performance-testing)
- [Data Integrity Testing](#data-integrity-testing)

## Overview

This guide provides comprehensive testing strategies for the Genre preservation approach in the Chinook database implementation. The Genre preservation strategy maintains the original `genres` table while integrating with the single taxonomy system using the `aliziodev/laravel-taxonomy` package for enhanced functionality.

**Testing Framework**: All examples use Pest PHP with describe/it blocks following modern Laravel 12 patterns.

**Key Testing Areas**:

- Genre table preservation and backward compatibility
- Single taxonomy system integration with Genre compatibility layer
- Data migration validation from original chinook.sql format
- Performance impact assessment
- ChinookGenre compatibility interface testing

## Genre Preservation Strategy Testing

### Core Genre Preservation Tests

```php
<?php

use App\Models\Genre;
use App\Models\ChinookTrack;
use App\Models\ChinookGenre;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\RefreshDatabase;

describe('Genre Preservation Strategy', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
    });

    describe('Original Genre Table Preservation', function () {
        it('preserves all original genre records', function () {
            // Verify all 25 original genres exist
            expect(Genre::count())->toBe(25);
            
            // Verify specific genres from chinook.sql
            expect(Genre::where('name', 'Rock')->exists())->toBeTrue();
            expect(Genre::where('name', 'Jazz')->exists())->toBeTrue();
            expect(Genre::where('name', 'Metal')->exists())->toBeTrue();
            expect(Genre::where('name', 'Alternative & Punk')->exists())->toBeTrue();
        });

        it('maintains original genre IDs and relationships', function () {
            $rockGenre = Genre::where('name', 'Rock')->first();
            $track = Track::factory()->create(['genre_id' => $rockGenre->id]);
            
            expect($track->genre_id)->toBe($rockGenre->id);
            expect($track->genre->name)->toBe('Rock');
        });

        it('preserves genre table structure', function () {
            $genre = Genre::first();
            
            // Verify original columns exist
            expect($genre->getConnection()->getSchemaBuilder()->hasColumn('genres', 'id'))->toBeTrue();
            expect($genre->getConnection()->getSchemaBuilder()->hasColumn('genres', 'name'))->toBeTrue();
            
            // Verify no unexpected modifications
            $columns = $genre->getConnection()->getSchemaBuilder()->getColumnListing('genres');
            expect($columns)->toContain('id', 'name');
        });
    });

    describe('Genre-Taxonomy Integration', function () {
        it('imports genre records to taxonomy system', function () {
            // Run genre-to-taxonomy import
            $this->artisan('chinook:import-genres-to-taxonomy');

            // Verify taxonomy creation
            $genreCount = Genre::count();
            $genreTaxonomyCount = Taxonomy::where('type', 'genre')->count();

            expect($genreTaxonomyCount)->toBe($genreCount);
        });

        it('maintains genre-taxonomy relationship mapping', function () {
            $this->artisan('chinook:import-genres-to-taxonomy');

            $rockGenre = Genre::where('name', 'Rock')->first();
            $rockTaxonomy = Taxonomy::where('type', 'genre')
                                  ->where('name', 'Rock')
                                  ->first();

            expect($rockTaxonomy)->not->toBeNull();
            expect($rockTaxonomy->meta['chinook_genre_id'])->toBe($rockGenre->id);
        });

        it('preserves genre metadata in taxonomies', function () {
            $this->artisan('chinook:import-genres-to-taxonomy');

            $genre = Genre::first();
            $taxonomy = Taxonomy::where('type', 'genre')
                               ->where('meta->chinook_genre_id', $genre->id)
                               ->first();

            expect($taxonomy->meta)->toHaveKey('chinook_genre_id');
            expect($taxonomy->meta)->toHaveKey('original_name');
            expect($taxonomy->meta['chinook_genre_id'])->toBe($genre->id);
            expect($taxonomy->meta['original_name'])->toBe($genre->name);
        });
    });

    private function seedOriginalGenres(): void
    {
        // Seed the original 25 genres from chinook.sql
        $originalGenres = [
            'Rock', 'Jazz', 'Metal', 'Alternative & Punk', 'Rock And Roll',
            'Blues', 'Latin', 'Reggae', 'Pop', 'Soundtrack',
            'Bossa Nova', 'Easy Listening', 'Heavy Metal', 'R&B/Soul',
            'Electronica/Dance', 'World', 'Hip Hop/Rap', 'Science Fiction',
            'TV Shows', 'Sci Fi & Fantasy', 'Drama', 'Comedy',
            'Alternative', 'Classical', 'Opera'
        ];

        foreach ($originalGenres as $index => $genreName) {
            Genre::create([
                'id' => $index + 1,
                'name' => $genreName
            ]);
        }
    }
});
```

## Backward Compatibility Testing

### Legacy Code Compatibility Tests

```php
describe('Backward Compatibility', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
    });

    describe('Existing Track-Genre Relationships', function () {
        it('maintains existing track-genre foreign key relationships', function () {
            $genre = Genre::first();
            $track = Track::factory()->create(['genre_id' => $genre->id]);
            
            // Verify relationship works as before
            expect($track->genre)->not->toBeNull();
            expect($track->genre->id)->toBe($genre->id);
            expect($track->genre_id)->toBe($genre->id);
        });

        it('supports legacy genre queries', function () {
            $rockGenre = Genre::where('name', 'Rock')->first();
            Track::factory()->count(5)->create(['genre_id' => $rockGenre->id]);
            
            // Legacy query patterns should still work
            $rockTracks = Track::where('genre_id', $rockGenre->id)->get();
            expect($rockTracks)->toHaveCount(5);
            
            $tracksWithGenre = Track::with('genre')->get();
            expect($tracksWithGenre->first()->genre)->not->toBeNull();
        });

        it('preserves genre-based filtering and sorting', function () {
            $genres = Genre::take(3)->get();
            foreach ($genres as $genre) {
                Track::factory()->count(2)->create(['genre_id' => $genre->id]);
            }
            
            // Test genre-based filtering
            $firstGenreTracks = Track::whereHas('genre', function ($query) use ($genres) {
                $query->where('name', $genres->first()->name);
            })->get();
            
            expect($firstGenreTracks)->toHaveCount(2);
            
            // Test genre-based sorting
            $sortedTracks = Track::join('genres', 'tracks.genre_id', '=', 'genres.id')
                                ->orderBy('genres.name')
                                ->select('tracks.*')
                                ->get();
            
            expect($sortedTracks)->toHaveCount(6);
        });
    });

    describe('API Compatibility', function () {
        it('maintains genre API endpoints', function () {
            $response = $this->getJson('/api/genres');
            
            $response->assertStatus(200)
                    ->assertJsonStructure([
                        'data' => [
                            '*' => ['id', 'name']
                        ]
                    ]);
        });

        it('supports legacy genre-based track filtering', function () {
            $genre = Genre::first();
            Track::factory()->count(3)->create(['genre_id' => $genre->id]);
            
            $response = $this->getJson("/api/tracks?genre_id={$genre->id}");
            
            $response->assertStatus(200);
            expect($response->json('data'))->toHaveCount(3);
        });
    });
});
```

## Migration Testing

### Genre-to-Taxonomy Import Tests

```php
describe('Genre Import Testing', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
        $this->createTestTracks();
    });

    describe('Import Process Validation', function () {
        it('imports genres to taxonomy system without data loss', function () {
            $originalGenreCount = Genre::count();
            $originalTrackCount = ChinookTrack::count();

            // Run import
            $this->artisan('chinook:import-genres-to-taxonomy');

            // Verify no data loss
            expect(Genre::count())->toBe($originalGenreCount);
            expect(ChinookTrack::count())->toBe($originalTrackCount);
            expect(Taxonomy::where('type', 'genre')->count())->toBe($originalGenreCount);
        });

        it('creates taxonomy relationships during import', function () {
            ChinookTrack::factory()->count(5)->create();

            $this->artisan('chinook:import-genres-to-taxonomy');

            // Verify taxonomy relationships can be created
            $track = ChinookTrack::first();
            $genreTaxonomy = Taxonomy::where('type', 'genre')->first();

            $track->attachTaxonomy($genreTaxonomy->id, ['source' => 'chinook_import']);

            expect($track->taxonomies)->toHaveCount(1);
            expect($track->taxonomies->first()->type)->toBe('genre');
        });

        it('validates import rollback capability', function () {
            $this->artisan('chinook:import-genres-to-taxonomy');

            // Verify import completed
            expect(Taxonomy::where('type', 'genre')->count())->toBeGreaterThan(0);

            // Test rollback
            $this->artisan('chinook:rollback-genre-import');

            // Verify rollback (taxonomies removed, genres preserved)
            expect(Taxonomy::where('type', 'genre')->count())->toBe(0);
            expect(Genre::count())->toBeGreaterThan(0);
        });
    });

    private function createTestTracks(): void
    {
        $genres = Genre::take(5)->get();
        foreach ($genres as $genre) {
            ChinookTrack::factory()->count(3)->create();
        }
    }
});
```

## Taxonomy Integration Testing

### Single Taxonomy System Tests

```php
<?php

use App\Models\ChinookTrack;
use App\Models\ChinookGenre;
use Aliziodev\LaravelTaxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\RefreshDatabase;

describe('Single Taxonomy System Integration', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
        $this->artisan('chinook:import-genres-to-taxonomy');
    });

    describe('Track Taxonomy Relationships', function () {
        it('supports taxonomy relationships for tracks', function () {
            $track = ChinookTrack::factory()->create();

            // Create genre taxonomy
            $genreTaxonomy = Taxonomy::create([
                'name' => 'Rock',
                'type' => 'genre',
                'slug' => 'rock'
            ]);

            // Attach taxonomy with metadata
            $track->attachTaxonomy($genreTaxonomy->id, [
                'is_primary' => true,
                'source' => 'chinook_import'
            ]);

            // Verify taxonomy relationship
            expect($track->taxonomies)->toHaveCount(1);
            expect($track->taxonomies->first()->name)->toBe('Rock');
            expect($track->taxonomies->first()->type)->toBe('genre');
        });

        it('supports multiple taxonomy types', function () {
            $track = ChinookTrack::factory()->create();

            $genreTaxonomy = Taxonomy::create(['name' => 'Rock', 'type' => 'genre']);
            $moodTaxonomy = Taxonomy::create(['name' => 'Energetic', 'type' => 'mood']);
            $themeTaxonomy = Taxonomy::create(['name' => 'Adventure', 'type' => 'theme']);

            // Attach multiple taxonomies
            $track->attachTaxonomy($genreTaxonomy->id, ['is_primary' => true]);
            $track->attachTaxonomy($moodTaxonomy->id);
            $track->attachTaxonomy($themeTaxonomy->id);

            // Verify all taxonomies attached
            expect($track->taxonomies)->toHaveCount(3);

            // Test type-specific queries
            $genreTaxonomies = $track->taxonomies()->where('type', 'genre')->get();
            $moodTaxonomies = $track->taxonomies()->where('type', 'mood')->get();
            $themeTaxonomies = $track->taxonomies()->where('type', 'theme')->get();

            expect($genreTaxonomies)->toHaveCount(1);
            expect($moodTaxonomies)->toHaveCount(1);
            expect($themeTaxonomies)->toHaveCount(1);
        });

        it('supports primary genre identification', function () {
            $track = ChinookTrack::factory()->create();

            $rockTaxonomy = Taxonomy::create(['name' => 'Rock', 'type' => 'genre']);
            $jazzTaxonomy = Taxonomy::create(['name' => 'Jazz', 'type' => 'genre']);

            // Attach multiple genres with primary designation
            $track->attachTaxonomy($rockTaxonomy->id, ['is_primary' => true]);
            $track->attachTaxonomy($jazzTaxonomy->id, ['is_primary' => false]);

            // Test primary genre method
            $primaryGenre = $track->primaryGenre();

            expect($primaryGenre)->not->toBeNull();
            expect($primaryGenre->name)->toBe('Rock');
        });
    });

    describe('Taxonomy Hierarchy Support', function () {
        it('creates hierarchical genre taxonomies', function () {
            $musicRoot = Taxonomy::create([
                'name' => 'Music',
                'type' => 'genre',
                'slug' => 'music'
            ]);

            $rockTaxonomy = Taxonomy::create([
                'name' => 'Rock',
                'type' => 'genre',
                'slug' => 'rock',
                'parent_id' => $musicRoot->id
            ]);

            $hardRockTaxonomy = Taxonomy::create([
                'name' => 'Hard Rock',
                'type' => 'genre',
                'slug' => 'hard-rock',
                'parent_id' => $rockTaxonomy->id
            ]);

            // Verify hierarchy
            expect($rockTaxonomy->parent_id)->toBe($musicRoot->id);
            expect($hardRockTaxonomy->parent_id)->toBe($rockTaxonomy->id);

            // Test hierarchical queries
            $rockChildren = $rockTaxonomy->children()->get();
            expect($rockChildren)->toHaveCount(1);
            expect($rockChildren->first()->name)->toBe('Hard Rock');
        });

        it('supports taxonomy tree traversal', function () {
            // Create taxonomy tree
            $music = Taxonomy::create(['name' => 'Music', 'type' => 'genre']);
            $rock = Taxonomy::create(['name' => 'Rock', 'type' => 'genre', 'parent_id' => $music->id]);
            $hardRock = Taxonomy::create(['name' => 'Hard Rock', 'type' => 'genre', 'parent_id' => $rock->id]);
            $metal = Taxonomy::create(['name' => 'Metal', 'type' => 'genre', 'parent_id' => $rock->id]);

            // Test tree traversal methods
            $musicDescendants = $music->descendants()->get();
            $rockAncestors = $hardRock->ancestors()->get();

            expect($musicDescendants)->toHaveCount(3); // Rock, Hard Rock, Metal
            expect($rockAncestors)->toHaveCount(2); // Music, Rock
        });
    });
});
```

## Performance Testing

### Genre Preservation Performance Tests

```php
describe('Performance Impact Testing', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
        $this->createLargeDataset();
    });

    describe('Query Performance Comparison', function () {
        it('measures genre-only query performance', function () {
            $startTime = microtime(true);

            $rockTracks = Track::whereHas('genre', function ($query) {
                $query->where('name', 'Rock');
            })->get();

            $genreQueryTime = (microtime(true) - $startTime) * 1000;

            expect($genreQueryTime)->toBeLessThan(100); // Under 100ms
            expect($rockTracks->count())->toBeGreaterThan(0);
        });

        it('measures taxonomy system query performance', function () {
            $this->artisan('chinook:import-genres-to-taxonomy');

            $startTime = microtime(true);

            $rockTracks = ChinookTrack::whereHas('taxonomies', function ($query) {
                $query->where('type', 'genre')
                      ->where('name', 'Rock');
            })->get();

            $taxonomyQueryTime = (microtime(true) - $startTime) * 1000;

            expect($taxonomyQueryTime)->toBeLessThan(120); // Optimized single system
            expect($rockTracks->count())->toBeGreaterThan(0);
        });

        it('compares memory usage between approaches', function () {
            $memoryBefore = memory_get_usage();

            // Load tracks with genres
            $tracksWithGenres = Track::with('genre')->take(1000)->get();

            $genreMemoryUsage = memory_get_usage() - $memoryBefore;

            // Reset and test with categories
            unset($tracksWithGenres);
            gc_collect_cycles();

            $this->artisan('chinook:migrate-genres-to-categories');

            $memoryBefore = memory_get_usage();

            $tracksWithCategories = Track::with(['categories' => function ($query) {
                $query->where('type', CategoryType::GENRE);
            }])->take(1000)->get();

            $categoryMemoryUsage = memory_get_usage() - $memoryBefore;

            // Category approach should not use significantly more memory
            expect($categoryMemoryUsage)->toBeLessThan($genreMemoryUsage * 1.5);
        });
    });

    private function createLargeDataset(): void
    {
        $genres = Genre::all();

        foreach ($genres as $genre) {
            Track::factory()->count(100)->create(['genre_id' => $genre->id]);
        }
    }
});
```

## Data Integrity Testing

### Comprehensive Data Integrity Tests

```php
describe('Data Integrity Validation', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
    });

    describe('Referential Integrity', function () {
        it('maintains foreign key constraints', function () {
            $genre = Genre::first();
            $track = Track::factory()->create(['genre_id' => $genre->id]);

            // Attempt to delete genre with tracks should fail
            expect(fn() => $genre->delete())
                ->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('validates category-genre mapping integrity', function () {
            $this->artisan('chinook:migrate-genres-to-categories');

            $genre = Genre::first();
            $category = Category::where('type', CategoryType::GENRE)
                               ->where('metadata->genre_id', $genre->id)
                               ->first();

            expect($category)->not->toBeNull();
            expect($category->metadata['genre_id'])->toBe($genre->id);
        });

        it('prevents orphaned category relationships', function () {
            $this->artisan('chinook:migrate-genres-to-categories');

            $track = Track::factory()->create(['genre_id' => Genre::first()->id]);
            $category = $track->categories()->where('type', CategoryType::GENRE)->first();

            // Delete category should clean up relationships
            $category->delete();

            expect($track->fresh()->categories()->where('type', CategoryType::GENRE)->count())->toBe(0);
        });
    });

    describe('Data Consistency Validation', function () {
        it('validates genre-category name consistency', function () {
            $this->artisan('chinook:migrate-genres-to-categories');

            $genres = Genre::all();

            foreach ($genres as $genre) {
                $category = Category::where('type', CategoryType::GENRE)
                                  ->where('metadata->genre_id', $genre->id)
                                  ->first();

                expect($category->name)->toBe($genre->name);
            }
        });

        it('validates unique constraints', function () {
            // Test genre name uniqueness
            expect(fn() => Genre::create(['name' => Genre::first()->name]))
                ->toThrow(\Illuminate\Database\QueryException::class);

            // Test category name uniqueness within type
            $this->artisan('chinook:migrate-genres-to-categories');

            $existingCategory = Category::where('type', CategoryType::GENRE)->first();

            expect(fn() => Category::create([
                'type' => CategoryType::GENRE,
                'name' => $existingCategory->name
            ]))->toThrow(\Illuminate\Database\QueryException::class);
        });
    });
});
```
